#!/usr/bin/env python3
"""
Crop window calculation module for intelligent video reframing

This module calculates optimal crop windows with temporal smoothing
for stable and visually appealing video crops.
"""

import logging
from collections import deque
from typing import Optional, Tuple

from ..models.data_classes import FaceDetection, CropWindow, GroupFaceBounds
from ..face_detection.face_centering import FaceCenteringAlgorithm


class CropWindowCalculator:
    """
    Calculates optimal crop windows with temporal smoothing
    Enhanced with intelligent two-face positioning for vertical video
    """

    def __init__(self, smoothing_factor: float = 0.3, margin: float = 0.2):
        self.smoothing_factor = smoothing_factor
        self.margin = margin
        self.previous_crops = deque(maxlen=5)  # Keep last 5 crop windows for smoothing
        self.logger = logging.getLogger(__name__)

        # Initialize professional face centering algorithm
        self.face_centering = FaceCenteringAlgorithm()



    def calculate_crop_window(self, primary_face: Optional[FaceDetection],
                            frame_width: int, frame_height: int,
                            target_width: int, target_height: int,
                            timestamp: float,
                            group_bounds: Optional[GroupFaceBounds] = None) -> CropWindow:
        """
        Calculate optimal crop window based on primary face with temporal smoothing

        Args:
            primary_face: Primary face detection result
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height
            timestamp: Current timestamp
            group_bounds: Optional group face bounds for multi-face tracking

        Returns:
            CropWindow object
        """
        # Prioritize group bounds for multi-face scenarios
        if group_bounds and group_bounds.face_count >= 2:
            # Check if this requires segment-based processing (flagged by negative confidence offset)
            if group_bounds.confidence < 0.9 and group_bounds.width > target_width:
                self.logger.warning(f"🎭 Group bounds ({group_bounds.width}px) exceed target width ({target_width}px)")
                self.logger.warning(f"🎯 Using center-focused crop to minimize face cutoff")

                # For wide group bounds, use a center-focused approach
                crop_x, crop_y = self._calculate_wide_group_crop(
                    group_bounds, frame_width, frame_height, target_width, target_height
                )
                confidence = group_bounds.confidence + 0.001  # Restore original confidence
            else:
                # Use normal group face tracking for multiple faces
                crop_x, crop_y = self._calculate_group_based_crop(
                    group_bounds, frame_width, frame_height, target_width, target_height
                )
                confidence = group_bounds.confidence
        elif primary_face:
            # Calculate crop based on single face position
            crop_x, crop_y = self._calculate_face_based_crop(
                primary_face, frame_width, frame_height, target_width, target_height
            )
            confidence = primary_face.confidence
        else:
            # Improved fallback logic: use previous successful detection if available
            if self.previous_crops:
                # Find the most recent crop with good confidence
                recent_good_crops = [crop for crop in self.previous_crops if crop.confidence > 0.3]
                if recent_good_crops:
                    # Use the most recent good crop position
                    last_good_crop = recent_good_crops[-1]
                    crop_x = last_good_crop.x
                    crop_y = last_good_crop.y
                    confidence = 0.1  # Low confidence but better than center crop
                else:
                    # Fallback to center crop
                    crop_x = (frame_width - target_width) // 2
                    crop_y = (frame_height - target_height) // 2
                    confidence = 0.0
            else:
                # Fallback to center crop
                crop_x = (frame_width - target_width) // 2
                crop_y = (frame_height - target_height) // 2
                confidence = 0.0

        # Apply temporal smoothing
        if self.previous_crops:
            crop_x, crop_y = self._apply_temporal_smoothing(crop_x, crop_y)

        # Ensure crop is within bounds
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        crop_window = CropWindow(crop_x, crop_y, target_width, target_height, confidence, timestamp)
        self.previous_crops.append(crop_window)

        return crop_window

    def _calculate_face_based_crop(self, face: FaceDetection, frame_width: int, frame_height: int,
                                 target_width: int, target_height: int) -> Tuple[int, int]:
        """Professional face centering using advanced cinematography principles"""

        # Use professional face centering algorithm
        crop_x, crop_y = self.face_centering.calculate_optimal_crop_position(
            face, frame_width, frame_height, target_width, target_height
        )

        # Log centering quality for monitoring
        if self.logger.isEnabledFor(logging.DEBUG):
            metrics = self.face_centering.assess_centering_quality(
                face, crop_x, crop_y, target_width, target_height
            )
            self.logger.debug(f"🎯 Professional centering metrics: "
                            f"horizontal_dev={metrics.horizontal_deviation:.3f}, "
                            f"vertical_score={metrics.vertical_positioning_score:.3f}, "
                            f"composition_score={metrics.composition_score:.3f}")

        return crop_x, crop_y

    def _calculate_group_based_crop(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                  target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Advanced crop position calculation for multi-face tracking with intelligent two-face handling

        Enhanced for optimal vertical video cropping when faces are positioned on left and right sides.
        Uses sophisticated algorithms and optional OpenAI optimization for compelling social media content.

        Args:
            group_bounds: Group face bounding box
            frame_width: Original frame width (e.g., 1280)
            frame_height: Original frame height (e.g., 720)
            target_width: Target crop width (e.g., 720 for vertical)
            target_height: Target crop height (e.g., 1280 for vertical)

        Returns:
            Tuple of (crop_x, crop_y) - optimal crop position
        """
        # Use professional multi-face centering algorithm
        crop_x, crop_y = self.face_centering.calculate_multi_face_crop_position(
            group_bounds, frame_width, frame_height, target_width, target_height
        )

        # Log multi-face positioning for monitoring
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(f"🎭 Professional multi-face positioning: "
                            f"faces={group_bounds.face_count}, "
                            f"crop=({crop_x}, {crop_y}), "
                            f"group_center=({group_bounds.center_x}, {group_bounds.center_y})")

        return crop_x, crop_y

    def _calculate_two_face_vertical_crop(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                        target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Advanced crop calculation for two faces in vertical format

        Optimized for scenarios where faces are positioned on left and right sides of the frame.
        Uses sophisticated algorithms to ensure both faces are well-centered and visible in the vertical crop.
        Enhanced with better boundary checking and face separation distance consideration.
        """
        # Calculate key metrics for intelligent positioning
        group_to_target_ratio = group_bounds.width / target_width
        available_margin = target_width - group_bounds.width

        # Calculate safety margins to prevent face cutoff
        min_safety_margin = max(20, int(target_width * 0.03))  # Minimum 20px or 3% of target width

        self.logger.debug(f"Two-face crop analysis: group_width={group_bounds.width}, target_width={target_width}, "
                         f"ratio={group_to_target_ratio:.2f}, available_margin={available_margin}px")

        # Enhanced positioning strategy with better boundary awareness
        if group_to_target_ratio > 0.98:  # Group is extremely wide (>98% of target width)
            # Critical case: faces are very far apart, need ultra-precise positioning
            crop_x = self._handle_critical_face_separation(group_bounds, frame_width, target_width, min_safety_margin)
            self.logger.info(f"🎭 Critical face separation detected (ratio: {group_to_target_ratio:.2f}), "
                           f"using ultra-precise positioning: x={crop_x}")

        elif group_to_target_ratio > 0.90:  # Group is very wide (90-98% of target width)
            # Very challenging case: faces are far apart, need precise positioning
            crop_x = self._handle_wide_face_separation(group_bounds, frame_width, target_width, min_safety_margin)
            self.logger.info(f"🎭 Wide face separation detected (ratio: {group_to_target_ratio:.2f}), "
                           f"using precise positioning: x={crop_x}")

        elif group_to_target_ratio > 0.70:  # Group is moderately wide (70-90% of target width)
            # Standard case: faces are well separated but manageable
            crop_x = self._handle_moderate_face_separation(group_bounds, frame_width, target_width, min_safety_margin)
            self.logger.debug(f"🎭 Moderate face separation (ratio: {group_to_target_ratio:.2f}), "
                            f"using balanced positioning: x={crop_x}")

        else:  # Group fits comfortably (<70% of target width)
            # Easy case: faces are close enough to center normally with optimal composition
            crop_x = self._handle_comfortable_face_positioning(group_bounds, frame_width, target_width)
            self.logger.debug(f"🎭 Comfortable face positioning (ratio: {group_to_target_ratio:.2f}), "
                            f"using optimized center positioning: x={crop_x}")

        # Note: OpenAI optimization removed to save money
        # Using standard crop calculation for all cases

        # Calculate optimal vertical position for social media composition
        # Position faces in upper-middle area for better engagement
        desired_group_center_y = int(target_height * 0.28)  # Slightly higher for better composition
        crop_y = group_bounds.center_y - desired_group_center_y

        # Enhanced boundary checks with safety margins
        max_crop_x = frame_width - target_width
        max_crop_y = frame_height - target_height

        crop_x = max(0, min(max_crop_x, crop_x))
        crop_y = max(0, min(max_crop_y, crop_y))

        # Final validation: ensure we're not cutting off the group
        # Check if crop window includes the entire group with safety margins
        group_left_with_margin = group_bounds.x - min_safety_margin
        group_right_with_margin = group_bounds.x + group_bounds.width + min_safety_margin

        # Adjust crop position if it doesn't include the group properly
        if crop_x > group_left_with_margin:
            crop_x = max(0, group_left_with_margin)
        if crop_x + target_width < group_right_with_margin:
            # Try to shift right to include the group
            new_crop_x = group_right_with_margin - target_width
            if new_crop_x >= 0 and new_crop_x <= max_crop_x:
                crop_x = new_crop_x
            else:
                # If we can't fit the entire group, center it as best as possible
                crop_x = max(0, min(max_crop_x, group_bounds.center_x - target_width // 2))

        self.logger.debug(f"Final two-face crop position: x={crop_x}, y={crop_y} "
                         f"(group bounds: x={group_bounds.x}, width={group_bounds.width})")

        return crop_x, crop_y

    def _handle_critical_face_separation(self, group_bounds: GroupFaceBounds, frame_width: int,
                                        target_width: int, min_safety_margin: int) -> int:
        """Handle cases where faces are extremely far apart (>98% of target width)"""
        # Ultra-precise positioning for critical cases
        # Calculate absolute minimum and maximum positions to include both faces
        absolute_min_x = max(0, group_bounds.x - min_safety_margin)
        absolute_max_x = min(frame_width - target_width,
                           group_bounds.x + group_bounds.width + min_safety_margin - target_width)

        # If the range is invalid, prioritize including the group
        if absolute_min_x > absolute_max_x:
            # Group is too wide for target, center on group as best as possible
            crop_x = max(0, min(frame_width - target_width, group_bounds.center_x - target_width // 2))
            self.logger.warning(f"Group too wide for target, using center positioning: x={crop_x}")
        else:
            # Find the position that best centers the group within the valid range
            ideal_center_x = group_bounds.center_x - target_width // 2
            crop_x = max(absolute_min_x, min(absolute_max_x, ideal_center_x))

        return crop_x

    def _handle_wide_face_separation(self, group_bounds: GroupFaceBounds, frame_width: int,
                                   target_width: int, min_safety_margin: int) -> int:
        """Handle cases where faces are very far apart (90-98% of target width)"""
        # Enhanced logic with better safety margins
        margin_percent = max(0.015, min_safety_margin / target_width)  # At least 1.5% or safety margin

        min_crop_x = max(0, group_bounds.x - int(target_width * margin_percent))
        max_crop_x = min(frame_width - target_width,
                        group_bounds.x + group_bounds.width - target_width + int(target_width * margin_percent))

        # Choose the position that best centers the group while ensuring inclusion
        ideal_crop_x = group_bounds.center_x - target_width // 2
        crop_x = max(min_crop_x, min(max_crop_x, ideal_crop_x))

        return crop_x

    def _handle_moderate_face_separation(self, group_bounds: GroupFaceBounds, frame_width: int,
                                       target_width: int, min_safety_margin: int) -> int:
        """Handle cases where faces are moderately separated (70-90% of target width)"""
        # Use a balanced approach with dynamic margin calculation
        margin_percent = max(0.03, min_safety_margin / target_width)  # At least 3% or safety margin

        min_crop_x = max(0, group_bounds.x - int(target_width * margin_percent))
        max_crop_x = min(frame_width - target_width,
                        group_bounds.x + group_bounds.width - target_width + int(target_width * margin_percent))

        # Prefer centering but respect boundaries with better composition
        ideal_crop_x = group_bounds.center_x - target_width // 2
        crop_x = max(min_crop_x, min(max_crop_x, ideal_crop_x))

        return crop_x

    def _handle_comfortable_face_positioning(self, group_bounds: GroupFaceBounds, frame_width: int,
                                           target_width: int) -> int:
        """Handle cases where faces fit comfortably (<70% of target width)"""
        # For comfortable positioning, we can optimize for best composition
        # Use the rule of thirds for more engaging social media content

        # Calculate optimal positions using rule of thirds
        third_position_left = int(target_width * 0.33)
        third_position_right = int(target_width * 0.67)
        center_position = target_width // 2

        # Try to position the group center at one of these optimal positions
        crop_options = [
            group_bounds.center_x - third_position_left,   # Group center at left third
            group_bounds.center_x - center_position,       # Group center at center
            group_bounds.center_x - third_position_right   # Group center at right third
        ]

        # Choose the option that keeps the crop within frame bounds
        valid_crops = [x for x in crop_options if 0 <= x <= frame_width - target_width]

        if valid_crops:
            # Prefer center, then left third, then right third
            if group_bounds.center_x - center_position in valid_crops:
                crop_x = group_bounds.center_x - center_position
            else:
                crop_x = valid_crops[0]
        else:
            # Fallback to simple centering
            crop_x = max(0, min(frame_width - target_width, group_bounds.center_x - target_width // 2))

        return crop_x

    def _calculate_standard_vertical_crop(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                        target_width: int, target_height: int) -> Tuple[int, int]:
        """Standard vertical crop calculation for non-two-face scenarios"""
        # Center the group horizontally
        crop_x = group_bounds.center_x - target_width // 2

        # Position group in upper portion for better vertical composition
        desired_group_center_y = int(target_height * 0.3)
        crop_y = group_bounds.center_y - desired_group_center_y

        # Boundary checks
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        return crop_x, crop_y

    def _calculate_wide_group_crop(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                 target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Calculate crop for wide group bounds that exceed target width

        This method handles dual-face scenarios where faces are too far apart to fit
        in a single crop window. It uses a center-focused approach to minimize cutoff.

        Args:
            group_bounds: Group face bounding box
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            Tuple of (crop_x, crop_y) - optimal crop position
        """
        self.logger.info(f"🎭 Calculating wide group crop for bounds {group_bounds.width}x{group_bounds.height}")

        # For wide groups, center the crop on the group center
        # This ensures both faces are partially visible even if not fully contained
        crop_x = int(round(group_bounds.center_x - target_width / 2.0))

        # Position group in upper portion for better vertical composition
        desired_group_center_y = int(target_height * 0.3)
        crop_y = group_bounds.center_y - desired_group_center_y

        # Ensure crop is within frame bounds
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        self.logger.info(f"🎯 Wide group crop: ({crop_x}, {crop_y}) for group center ({group_bounds.center_x}, {group_bounds.center_y})")

        return crop_x, crop_y

    def _apply_temporal_smoothing(self, new_x: int, new_y: int) -> Tuple[int, int]:
        """Apply adaptive temporal smoothing to reduce jitter while preserving centering accuracy"""
        if not self.previous_crops:
            return new_x, new_y

        # Get the most recent crop position
        last_crop = self.previous_crops[-1]

        # Calculate movement distance
        movement_distance = ((new_x - last_crop.x) ** 2 + (new_y - last_crop.y) ** 2) ** 0.5

        # Adaptive smoothing: reduce smoothing for small movements to preserve precision
        if movement_distance < 10:  # Small movement - preserve professional centering
            adaptive_smoothing = self.smoothing_factor * 0.3  # Reduced smoothing
        elif movement_distance < 30:  # Medium movement
            adaptive_smoothing = self.smoothing_factor * 0.7  # Moderate smoothing
        else:  # Large movement - apply full smoothing
            adaptive_smoothing = self.smoothing_factor

        # Get weighted average of recent positions (emphasize recent crops)
        weights = [0.5, 0.3, 0.2]  # Most recent gets highest weight
        recent_crops = list(self.previous_crops)[-3:]  # Last 3 crops

        if len(recent_crops) >= len(weights):
            weighted_x = sum(crop.x * weight for crop, weight in zip(recent_crops, weights))
            weighted_y = sum(crop.y * weight for crop, weight in zip(recent_crops, weights))
        else:
            # Fallback to simple average
            weighted_x = sum(crop.x for crop in recent_crops) / len(recent_crops)
            weighted_y = sum(crop.y for crop in recent_crops) / len(recent_crops)

        # Apply adaptive smoothing
        smoothed_x = int(weighted_x * adaptive_smoothing + new_x * (1 - adaptive_smoothing))
        smoothed_y = int(weighted_y * adaptive_smoothing + new_y * (1 - adaptive_smoothing))

        return smoothed_x, smoothed_y
