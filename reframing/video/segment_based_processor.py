#!/usr/bin/env python3
"""
Segment-Based Video Processor

This module handles videos with mixed face count scenarios by processing different
segments with appropriate strategies (single-face centering vs dual-face stacking).
"""

import os
import subprocess
import tempfile
import logging
from typing import List, Dict, Optional
from pathlib import Path

from ..face_detection.engine import FaceDetectionEngine
from .dual_face_processor import DualFaceProcessor
from ..models.data_classes import FaceDetection


class SegmentBasedProcessor:
    """
    Processor for videos with mixed face count scenarios
    
    This class analyzes videos to detect segments with different face counts
    and applies appropriate processing strategies for each segment.
    """
    
    def __init__(self, face_engine: FaceDetectionEngine):
        self.logger = logging.getLogger(__name__)
        self.face_engine = face_engine
        self.dual_face_processor = DualFaceProcessor()
    
    def process_mixed_face_video(self, input_video: str, output_video: str, 
                               target_width: int = 720, target_height: int = 1280) -> bool:
        """
        Process a video with mixed face count scenarios
        
        Args:
            input_video: Path to input video
            output_video: Path to output video
            target_width: Target width for vertical video
            target_height: Target height for vertical video
            
        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Processing mixed face video: {input_video}")
        
        # Step 1: Analyze face count segments
        segments = self.face_engine.analyze_face_count_segments(input_video, sample_interval=1.0)
        
        if not segments:
            self.logger.error("No face segments detected")
            return False
        
        # Step 2: Process each segment based on face count
        segment_videos = []
        
        with tempfile.TemporaryDirectory() as temp_dir:
            for i, segment in enumerate(segments):
                segment_output = os.path.join(temp_dir, f"segment_{i:03d}.mp4")
                
                if self._process_segment(input_video, segment_output, segment, target_width, target_height):
                    segment_videos.append(segment_output)
                else:
                    self.logger.error(f"Failed to process segment {i}")
                    return False
            
            # Step 3: Concatenate all segments
            if len(segment_videos) == 1:
                # Only one segment, just copy it
                import shutil
                shutil.copy2(segment_videos[0], output_video)
            else:
                # Multiple segments, concatenate them
                if not self._concatenate_segments(segment_videos, output_video):
                    return False
        
        self.logger.info(f"Successfully processed mixed face video: {output_video}")
        return True
    
    def _process_segment(self, input_video: str, output_video: str, segment: Dict, 
                        target_width: int, target_height: int) -> bool:
        """
        Process a single segment based on its face count
        
        Args:
            input_video: Input video path
            output_video: Output video path
            segment: Segment data with face count and timing
            target_width: Target width
            target_height: Target height
            
        Returns:
            True if successful, False otherwise
        """
        face_count = segment['face_count']
        start_time = segment['start_time']
        end_time = segment['end_time']
        duration = end_time - start_time
        
        self.logger.info(f"Processing segment {start_time:.1f}s-{end_time:.1f}s ({duration:.1f}s) with {face_count} faces")
        
        if face_count == 0:
            # No faces - use center crop
            return self._process_no_face_segment(input_video, output_video, start_time, end_time, target_width, target_height)
        elif face_count == 1:
            # Single face - use face centering
            return self._process_single_face_segment(input_video, output_video, segment, target_width, target_height)
        elif face_count == 2:
            # Dual face - use stacking approach
            return self._process_dual_face_segment(input_video, output_video, segment, target_width)
        else:
            # Multiple faces (>2) - use group centering
            return self._process_multi_face_segment(input_video, output_video, segment, target_width, target_height)
    
    def _process_no_face_segment(self, input_video: str, output_video: str, 
                                start_time: float, end_time: float,
                                target_width: int, target_height: int) -> bool:
        """Process segment with no faces using center crop"""
        duration = end_time - start_time
        
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video,
            '-ss', str(start_time),
            '-t', str(duration),
            '-filter:v', f'crop={target_width}:{target_height}',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        return self._run_ffmpeg_command(cmd, "no-face segment")
    
    def _process_single_face_segment(self, input_video: str, output_video: str, 
                                   segment: Dict, target_width: int, target_height: int) -> bool:
        """Process segment with single face using face centering"""
        start_time = segment['start_time']
        end_time = segment['end_time']
        duration = end_time - start_time
        
        # Get face data from the segment samples
        faces = []
        for sample in segment['samples']:
            if sample['faces']:
                faces.extend(sample['faces'])
        
        if not faces:
            # Fallback to center crop
            return self._process_no_face_segment(input_video, output_video, start_time, end_time, target_width, target_height)
        
        # Use the largest face for positioning
        primary_face = max(faces, key=lambda f: f.width * f.height)
        
        # Calculate crop position to center the face
        face_center_x = primary_face.x + primary_face.width // 2
        face_center_y = primary_face.y + primary_face.height // 2
        
        crop_x = max(0, face_center_x - target_width // 2)
        crop_y = max(0, face_center_y - target_height // 2)
        
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video,
            '-ss', str(start_time),
            '-t', str(duration),
            '-filter:v', f'crop={target_width}:{target_height}:{crop_x}:{crop_y}',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        return self._run_ffmpeg_command(cmd, "single-face segment")
    
    def _process_dual_face_segment(self, input_video: str, output_video: str, 
                                 segment: Dict, target_width: int) -> bool:
        """Process segment with dual faces using stacking approach"""
        start_time = segment['start_time']
        end_time = segment['end_time']
        
        # Get face data from the segment samples
        faces = []
        for sample in segment['samples']:
            if len(sample['faces']) == 2:
                faces = sample['faces']
                break
        
        if len(faces) != 2:
            self.logger.warning(f"Expected 2 faces in dual-face segment, got {len(faces)}")
            # Fallback to center crop
            return self._process_no_face_segment(input_video, output_video, start_time, end_time, target_width, target_width * 16 // 9)
        
        return self.dual_face_processor.process_dual_face_segment(
            input_video, output_video, faces, start_time, end_time, target_width
        )
    
    def _process_multi_face_segment(self, input_video: str, output_video: str, 
                                  segment: Dict, target_width: int, target_height: int) -> bool:
        """Process segment with multiple faces (>2) using group centering"""
        start_time = segment['start_time']
        end_time = segment['end_time']
        duration = end_time - start_time
        
        # For now, use center crop for multi-face scenarios
        # This could be enhanced with group bounds calculation
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video,
            '-ss', str(start_time),
            '-t', str(duration),
            '-filter:v', f'crop={target_width}:{target_height}',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        return self._run_ffmpeg_command(cmd, "multi-face segment")
    
    def _concatenate_segments(self, segment_videos: List[str], output_video: str) -> bool:
        """Concatenate multiple segment videos into final output"""
        # Create concat file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for video in segment_videos:
                f.write(f"file '{video}'\n")
            concat_file = f.name
        
        try:
            cmd = [
                'ffmpeg', '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',
                output_video
            ]
            
            return self._run_ffmpeg_command(cmd, "segment concatenation")
        finally:
            os.unlink(concat_file)
    
    def _run_ffmpeg_command(self, cmd: List[str], operation: str) -> bool:
        """Run an ffmpeg command with error handling"""
        self.logger.debug(f"{operation}: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode != 0:
                self.logger.error(f"{operation} failed: {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            self.logger.error(f"{operation} timed out")
            return False
        except Exception as e:
            self.logger.error(f"{operation} error: {str(e)}")
            return False
