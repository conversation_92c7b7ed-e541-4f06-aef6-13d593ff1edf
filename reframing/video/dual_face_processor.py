#!/usr/bin/env python3
"""
Dual Face Video Processor

This module implements specialized processing for dual-face scenarios where two faces
need to be cropped separately and stacked vertically for optimal vertical video format.
"""

import os
import subprocess
import tempfile
import logging
from typing import List, Tuple, Optional
from pathlib import Path

from ..models.data_classes import FaceDetection


class DualFaceProcessor:
    """
    Processor for dual-face video segments using crop and stack approach
    
    This class implements the logic described in the user requirements:
    1. Crop left-most face
    2. Crop right-most face  
    3. Resize both to same width
    4. Stack vertically
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_dual_face_segment(self, input_video: str, output_video: str, 
                                faces: List[FaceDetection], start_time: float, 
                                end_time: float, target_width: int = 720) -> bool:
        """
        Process a video segment with dual faces using crop and stack approach
        
        Args:
            input_video: Path to input video
            output_video: Path to output video
            faces: List of detected faces (should be 2)
            start_time: Segment start time in seconds
            end_time: Segment end time in seconds
            target_width: Target width for vertical video (default: 720)
            
        Returns:
            True if successful, False otherwise
        """
        if len(faces) != 2:
            self.logger.error(f"Expected 2 faces, got {len(faces)}")
            return False
        
        self.logger.info(f"Processing dual-face segment {start_time:.1f}s-{end_time:.1f}s")
        
        # Sort faces by x position (left to right)
        sorted_faces = sorted(faces, key=lambda f: f.x)
        left_face = sorted_faces[0]
        right_face = sorted_faces[1]
        
        self.logger.info(f"Left face: {left_face.width}x{left_face.height} at ({left_face.x}, {left_face.y})")
        self.logger.info(f"Right face: {right_face.width}x{right_face.height} at ({right_face.x}, {right_face.y})")
        
        # Create temporary directory for intermediate files
        with tempfile.TemporaryDirectory() as temp_dir:
            face1_path = os.path.join(temp_dir, "face1.mp4")
            face2_path = os.path.join(temp_dir, "face2.mp4")
            face1_resized_path = os.path.join(temp_dir, "face1_resized.mp4")
            face2_resized_path = os.path.join(temp_dir, "face2_resized.mp4")
            
            # Step 1: Crop left face
            if not self._crop_face(input_video, face1_path, left_face, start_time, end_time):
                return False
            
            # Step 2: Crop right face
            if not self._crop_face(input_video, face2_path, right_face, start_time, end_time):
                return False
            
            # Step 3: Resize both faces to target width
            if not self._resize_face_video(face1_path, face1_resized_path, target_width):
                return False
            
            if not self._resize_face_video(face2_path, face2_resized_path, target_width):
                return False
            
            # Step 4: Stack vertically
            if not self._stack_faces_vertically(face1_resized_path, face2_resized_path, output_video):
                return False
        
        self.logger.info(f"Successfully created dual-face video: {output_video}")
        return True
    
    def _crop_face(self, input_video: str, output_video: str, face: FaceDetection, 
                   start_time: float, end_time: float) -> bool:
        """
        Crop a single face from the video
        
        Args:
            input_video: Input video path
            output_video: Output video path
            face: Face detection data
            start_time: Start time in seconds
            end_time: End time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        # Add some padding around the face
        padding = max(face.width, face.height) // 8  # 12.5% padding
        crop_x = max(0, face.x - padding)
        crop_y = max(0, face.y - padding)
        crop_w = face.width + 2 * padding
        crop_h = face.height + 2 * padding
        
        duration = end_time - start_time
        
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video,
            '-ss', str(start_time),
            '-t', str(duration),
            '-filter:v', f'crop={crop_w}:{crop_h}:{crop_x}:{crop_y}',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        self.logger.debug(f"Cropping face: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                self.logger.error(f"Face crop failed: {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            self.logger.error("Face crop timed out")
            return False
        except Exception as e:
            self.logger.error(f"Face crop error: {str(e)}")
            return False
    
    def _resize_face_video(self, input_video: str, output_video: str, target_width: int) -> bool:
        """
        Resize face video to target width while maintaining aspect ratio
        
        Args:
            input_video: Input video path
            output_video: Output video path
            target_width: Target width in pixels
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            'ffmpeg', '-y',
            '-i', input_video,
            '-vf', f'scale={target_width}:-1',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        self.logger.debug(f"Resizing face video: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                self.logger.error(f"Face resize failed: {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            self.logger.error("Face resize timed out")
            return False
        except Exception as e:
            self.logger.error(f"Face resize error: {str(e)}")
            return False
    
    def _stack_faces_vertically(self, face1_video: str, face2_video: str, output_video: str) -> bool:
        """
        Stack two face videos vertically
        
        Args:
            face1_video: First face video (top)
            face2_video: Second face video (bottom)
            output_video: Output stacked video
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            'ffmpeg', '-y',
            '-i', face1_video,
            '-i', face2_video,
            '-filter_complex', '[0:v][1:v]vstack=inputs=2',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '128k',
            output_video
        ]
        
        self.logger.debug(f"Stacking faces vertically: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                self.logger.error(f"Face stacking failed: {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            self.logger.error("Face stacking timed out")
            return False
        except Exception as e:
            self.logger.error(f"Face stacking error: {str(e)}")
            return False
    
    def calculate_dual_face_bounds(self, faces: List[FaceDetection]) -> Tuple[FaceDetection, FaceDetection]:
        """
        Calculate bounds for dual face processing
        
        Args:
            faces: List of detected faces
            
        Returns:
            Tuple of (left_face, right_face)
        """
        if len(faces) != 2:
            raise ValueError(f"Expected 2 faces, got {len(faces)}")
        
        # Sort faces by x position (left to right)
        sorted_faces = sorted(faces, key=lambda f: f.x)
        return sorted_faces[0], sorted_faces[1]
