#!/usr/bin/env python3
"""
Core dual face functionality test without MediaPipe dependencies
"""

import sys
import os
import logging
from pathlib import Path
from typing import List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_dual_face_processor_import():
    """Test importing the dual face processor"""
    logger = logging.getLogger(__name__)
    
    try:
        from reframing.video.dual_face_processor import DualFaceProcessor
        logger.info("✓ Successfully imported DualFaceProcessor")
        
        # Test initialization
        processor = DualFaceProcessor()
        logger.info("✓ Successfully initialized DualFaceProcessor")
        
        return processor
    except Exception as e:
        logger.error(f"✗ Failed to import DualFaceProcessor: {str(e)}")
        return None

def test_segment_processor_import():
    """Test importing the segment-based processor"""
    logger = logging.getLogger(__name__)
    
    try:
        # First test if we can import without the face engine
        from reframing.video.segment_based_processor import SegmentBasedProcessor
        logger.info("✓ Successfully imported SegmentBasedProcessor")
        
        return True
    except Exception as e:
        logger.error(f"✗ Failed to import SegmentBasedProcessor: {str(e)}")
        return False

def test_face_detection_data_classes():
    """Test the face detection data classes"""
    logger = logging.getLogger(__name__)
    
    try:
        from reframing.models.data_classes import FaceDetection, GroupFaceBounds
        logger.info("✓ Successfully imported data classes")
        
        # Test creating face detection objects
        face1 = FaceDetection(
            x=100, y=150, width=200, height=250, confidence=0.95,
            center_x=200.0, center_y=275.0
        )
        
        face2 = FaceDetection(
            x=400, y=180, width=180, height=220, confidence=0.92,
            center_x=490.0, center_y=290.0
        )
        
        logger.info(f"✓ Created face1: {face1.width}x{face1.height} at ({face1.x}, {face1.y})")
        logger.info(f"✓ Created face2: {face2.width}x{face2.height} at ({face2.x}, {face2.y})")
        
        # Test dual face bounds calculation
        processor = test_dual_face_processor_import()
        if processor:
            left_face, right_face = processor.calculate_dual_face_bounds([face1, face2])
            logger.info(f"✓ Left face: {left_face.width}x{left_face.height} at ({left_face.x}, {left_face.y})")
            logger.info(f"✓ Right face: {right_face.width}x{right_face.height} at ({right_face.x}, {right_face.y})")
        
        return [face1, face2]
    except Exception as e:
        logger.error(f"✗ Failed to test data classes: {str(e)}")
        return None

def test_ffmpeg_command_generation():
    """Test FFmpeg command generation without actually running them"""
    logger = logging.getLogger(__name__)
    
    faces = test_face_detection_data_classes()
    if not faces:
        return False
    
    processor = test_dual_face_processor_import()
    if not processor:
        return False
    
    logger.info("=== Testing FFmpeg Command Generation ===")
    
    # Simulate the commands that would be generated
    left_face, right_face = processor.calculate_dual_face_bounds(faces)
    
    # Calculate crop parameters
    padding = max(left_face.width, left_face.height) // 8
    left_crop_x = max(0, left_face.x - padding)
    left_crop_y = max(0, left_face.y - padding)
    left_crop_w = left_face.width + 2 * padding
    left_crop_h = left_face.height + 2 * padding
    
    right_crop_x = max(0, right_face.x - padding)
    right_crop_y = max(0, right_face.y - padding)
    right_crop_w = right_face.width + 2 * padding
    right_crop_h = right_face.height + 2 * padding
    
    target_width = 720
    
    logger.info("Generated FFmpeg commands:")
    logger.info("1. Crop left face:")
    logger.info(f"   ffmpeg -i input.mp4 -filter:v 'crop={left_crop_w}:{left_crop_h}:{left_crop_x}:{left_crop_y}' face1.mp4")
    
    logger.info("2. Crop right face:")
    logger.info(f"   ffmpeg -i input.mp4 -filter:v 'crop={right_crop_w}:{right_crop_h}:{right_crop_x}:{right_crop_y}' face2.mp4")
    
    logger.info("3. Resize both faces:")
    logger.info(f"   ffmpeg -i face1.mp4 -vf 'scale={target_width}:-1' face1_resized.mp4")
    logger.info(f"   ffmpeg -i face2.mp4 -vf 'scale={target_width}:-1' face2_resized.mp4")
    
    logger.info("4. Stack vertically:")
    logger.info("   ffmpeg -i face1_resized.mp4 -i face2_resized.mp4 -filter_complex '[0:v][1:v]vstack=inputs=2' output.mp4")
    
    return True

def test_segment_logic():
    """Test the segment processing logic"""
    logger = logging.getLogger(__name__)
    
    logger.info("=== Testing Segment Processing Logic ===")
    
    # Simulate video segments
    segments = [
        {'start_time': 0.0, 'end_time': 5.0, 'face_count': 1, 'samples': []},
        {'start_time': 5.0, 'end_time': 12.0, 'face_count': 2, 'samples': []},
        {'start_time': 12.0, 'end_time': 18.0, 'face_count': 1, 'samples': []},
        {'start_time': 18.0, 'end_time': 25.0, 'face_count': 2, 'samples': []},
    ]
    
    logger.info(f"Processing {len(segments)} segments:")
    
    for i, segment in enumerate(segments):
        duration = segment['end_time'] - segment['start_time']
        strategy = get_processing_strategy(segment['face_count'])
        logger.info(f"  Segment {i+1}: {segment['start_time']:.1f}s-{segment['end_time']:.1f}s ({duration:.1f}s)")
        logger.info(f"    Face count: {segment['face_count']}")
        logger.info(f"    Strategy: {strategy}")
    
    return True

def get_processing_strategy(face_count: int) -> str:
    """Get processing strategy description"""
    if face_count == 0:
        return "Center crop"
    elif face_count == 1:
        return "Single face centering"
    elif face_count == 2:
        return "Dual face stacking"
    else:
        return f"Multi-face group centering ({face_count} faces)"

def test_video_file_existence():
    """Test if the sample video file exists"""
    logger = logging.getLogger(__name__)
    
    video_path = "tests/sample/dual_face_test/dual_face_test.mp4"
    
    if os.path.exists(video_path):
        logger.info(f"✓ Sample video found: {video_path}")
        
        # Get basic file info
        file_size = os.path.getsize(video_path)
        logger.info(f"  File size: {file_size / (1024*1024):.1f} MB")
        return True
    else:
        logger.warning(f"✗ Sample video not found: {video_path}")
        return False

def main():
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Dual Face Core Functionality Test")
    logger.info("=" * 50)
    
    # Test 1: Basic imports
    logger.info("\n=== Test 1: Import Tests ===")
    dual_processor_ok = test_dual_face_processor_import() is not None
    segment_processor_ok = test_segment_processor_import()
    data_classes_ok = test_face_detection_data_classes() is not None
    
    # Test 2: FFmpeg command generation
    logger.info("\n=== Test 2: FFmpeg Command Generation ===")
    ffmpeg_ok = test_ffmpeg_command_generation()
    
    # Test 3: Segment logic
    logger.info("\n=== Test 3: Segment Processing Logic ===")
    segment_logic_ok = test_segment_logic()
    
    # Test 4: Video file
    logger.info("\n=== Test 4: Sample Video File ===")
    video_file_ok = test_video_file_existence()
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Results Summary:")
    logger.info(f"✓ Dual Face Processor Import: {'PASS' if dual_processor_ok else 'FAIL'}")
    logger.info(f"✓ Segment Processor Import: {'PASS' if segment_processor_ok else 'FAIL'}")
    logger.info(f"✓ Data Classes: {'PASS' if data_classes_ok else 'FAIL'}")
    logger.info(f"✓ FFmpeg Command Generation: {'PASS' if ffmpeg_ok else 'FAIL'}")
    logger.info(f"✓ Segment Logic: {'PASS' if segment_logic_ok else 'FAIL'}")
    logger.info(f"✓ Sample Video File: {'PASS' if video_file_ok else 'FAIL'}")
    
    all_passed = all([dual_processor_ok, segment_processor_ok, data_classes_ok, ffmpeg_ok, segment_logic_ok])
    
    if all_passed:
        logger.info("\n🎉 All core tests PASSED!")
        logger.info("The dual face implementation is ready for use.")
    else:
        logger.warning("\n⚠️  Some tests failed. Check the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
