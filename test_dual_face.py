#!/usr/bin/env python3
"""
Test script for dual face detection and group bounds logic
"""

import sys
import os
import cv2
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from reframing.face_detection.engine import FaceDetectionEngine
from reframing.crop.calculator import CropWindowCalculator
from reframing.video.segment_based_processor import SegmentBasedProcessor
from reframing.video.dual_face_processor import DualFaceProcessor

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def analyze_video_frames(video_path: str, output_dir: str):
    """Analyze frames from the dual face test video"""
    
    logger = logging.getLogger(__name__)
    
    # Initialize face detection engine
    face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)
    crop_calculator = CropWindowCalculator()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = frame_count / fps
    
    logger.info(f"Video properties: {frame_width}x{frame_height}, {fps:.2f} fps, {duration:.2f}s")
    
    # Target dimensions for vertical video
    target_width = 720
    target_height = 1280
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Sample frames every 2 seconds
    sample_interval = int(fps * 2)  # Every 2 seconds
    frame_positions = list(range(0, frame_count, sample_interval))
    
    results = []
    
    for i, frame_pos in enumerate(frame_positions):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
        ret, frame = cap.read()
        
        if not ret:
            continue
            
        timestamp = frame_pos / fps
        logger.info(f"\n--- Frame {frame_pos} (t={timestamp:.1f}s) ---")
        
        # Detect faces
        faces = face_engine.detect_faces(frame)
        logger.info(f"Detected {len(faces)} faces")
        
        # Calculate group bounds if multiple faces
        group_bounds = None
        if len(faces) >= 1:  # Calculate for both single and multiple faces
            group_bounds = face_engine.calculate_group_face_bounds(faces, frame_width, frame_height, target_width)
            if group_bounds:
                logger.info(f"Group bounds: {group_bounds.width}x{group_bounds.height} at ({group_bounds.x}, {group_bounds.y}), face_count={group_bounds.face_count}")
        
        # Find primary face
        primary_face = None
        if faces:
            primary_face = max(faces, key=lambda f: f.width * f.height)
            logger.info(f"Primary face: {primary_face.width}x{primary_face.height} at ({primary_face.x}, {primary_face.y}), confidence={primary_face.confidence:.3f}")
        
        # Calculate crop window
        crop_window = None
        if primary_face or group_bounds:
            crop_window = crop_calculator.calculate_crop_window(
                primary_face, frame_width, frame_height, target_width, target_height, timestamp, group_bounds
            )
            logger.info(f"Crop window: {crop_window.width}x{crop_window.height} at ({crop_window.x}, {crop_window.y}), confidence={crop_window.confidence:.3f}")
        
        # Draw visualization
        vis_frame = frame.copy()
        
        # Draw detected faces
        for j, face in enumerate(faces):
            color = (0, 255, 0) if face == primary_face else (0, 255, 255)
            cv2.rectangle(vis_frame, (face.x, face.y), (face.x + face.width, face.y + face.height), color, 2)
            cv2.putText(vis_frame, f"Face {j+1}", (face.x, face.y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Draw group bounds
        if group_bounds:
            cv2.rectangle(vis_frame, (group_bounds.x, group_bounds.y), 
                         (group_bounds.x + group_bounds.width, group_bounds.y + group_bounds.height), 
                         (255, 0, 0), 3)
            cv2.putText(vis_frame, f"Group ({group_bounds.face_count} faces)", 
                       (group_bounds.x, group_bounds.y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
        
        # Draw crop window
        if crop_window:
            cv2.rectangle(vis_frame, (crop_window.x, crop_window.y), 
                         (crop_window.x + crop_window.width, crop_window.y + crop_window.height), 
                         (0, 0, 255), 2)
            cv2.putText(vis_frame, "Crop Window", 
                       (crop_window.x, crop_window.y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Save visualization
        output_path = os.path.join(output_dir, f"frame_{i:03d}_t{timestamp:.1f}s.jpg")
        cv2.imwrite(output_path, vis_frame)
        logger.info(f"Saved visualization: {output_path}")
        
        # Store results
        results.append({
            'timestamp': timestamp,
            'frame_pos': frame_pos,
            'face_count': len(faces),
            'faces': [(f.x, f.y, f.width, f.height, f.confidence) for f in faces],
            'group_bounds': (group_bounds.x, group_bounds.y, group_bounds.width, group_bounds.height, group_bounds.face_count) if group_bounds else None,
            'crop_window': (crop_window.x, crop_window.y, crop_window.width, crop_window.height, crop_window.confidence) if crop_window else None
        })
    
    cap.release()
    
    # Save results summary
    summary_path = os.path.join(output_dir, "analysis_summary.txt")
    with open(summary_path, 'w') as f:
        f.write("Dual Face Test Video Analysis\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Video: {video_path}\n")
        f.write(f"Dimensions: {frame_width}x{frame_height}\n")
        f.write(f"Duration: {duration:.2f}s\n")
        f.write(f"Target crop: {target_width}x{target_height}\n\n")
        
        for result in results:
            f.write(f"Time {result['timestamp']:6.1f}s: {result['face_count']} faces\n")
            if result['group_bounds']:
                gb = result['group_bounds']
                f.write(f"  Group bounds: {gb[2]}x{gb[3]} at ({gb[0]}, {gb[1]}), {gb[4]} faces\n")
            if result['crop_window']:
                cw = result['crop_window']
                f.write(f"  Crop window: {cw[2]}x{cw[3]} at ({cw[0]}, {cw[1]}), conf={cw[4]:.3f}\n")
            f.write("\n")
    
    logger.info(f"Analysis complete. Results saved to {output_dir}")

def test_segment_based_processing(video_path: str, output_dir: str):
    """Test the new segment-based processing for dual-face scenarios"""

    logger = logging.getLogger(__name__)

    # Initialize face detection engine
    face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    logger.info("=== Testing Face Count Segment Analysis ===")

    # Test face count segment analysis
    segments = face_engine.analyze_face_count_segments(video_path, sample_interval=1.0)

    if not segments:
        logger.error("No segments detected")
        return

    # Save segment analysis
    segment_summary_path = os.path.join(output_dir, "segment_analysis.txt")
    with open(segment_summary_path, 'w') as f:
        f.write("Face Count Segment Analysis\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Video: {video_path}\n\n")

        for i, seg in enumerate(segments):
            duration = seg['end_time'] - seg['start_time']
            f.write(f"Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s ({duration:.1f}s)\n")
            f.write(f"  Face count: {seg['face_count']}\n")
            f.write(f"  Samples: {len(seg['samples'])}\n")
            f.write("\n")

    logger.info(f"Segment analysis saved to {segment_summary_path}")

    logger.info("=== Testing Segment-Based Video Processing ===")

    # Test segment-based processing
    processor = SegmentBasedProcessor(face_engine)
    output_video = os.path.join(output_dir, "dual_face_processed.mp4")

    success = processor.process_mixed_face_video(
        video_path, output_video, target_width=720, target_height=1280
    )

    if success:
        logger.info(f"Successfully created processed video: {output_video}")
    else:
        logger.error("Failed to process video")

    logger.info("=== Testing Individual Dual-Face Processing ===")

    # Find a dual-face segment for individual testing
    dual_face_segments = [seg for seg in segments if seg['face_count'] == 2]

    if dual_face_segments:
        test_segment = dual_face_segments[0]
        logger.info(f"Testing dual-face processing on segment: {test_segment['start_time']:.1f}s-{test_segment['end_time']:.1f}s")

        # Get faces from the segment
        faces = []
        for sample in test_segment['samples']:
            if len(sample['faces']) == 2:
                faces = sample['faces']
                break

        if faces:
            dual_processor = DualFaceProcessor()
            dual_output = os.path.join(output_dir, "dual_face_segment_test.mp4")

            success = dual_processor.process_dual_face_segment(
                video_path, dual_output, faces,
                test_segment['start_time'], test_segment['end_time'], target_width=720
            )

            if success:
                logger.info(f"Successfully created dual-face segment video: {dual_output}")
            else:
                logger.error("Failed to process dual-face segment")
        else:
            logger.warning("No dual faces found in dual-face segment")
    else:
        logger.info("No dual-face segments found for individual testing")

def main():
    setup_logging()
    logger = logging.getLogger(__name__)

    video_path = "tests/sample/dual_face_test/dual_face_test.mp4"
    analysis_output_dir = "output/dual_face_analysis"
    processing_output_dir = "output/dual_face_processing"

    if not os.path.exists(video_path):
        logger.error(f"Video file not found: {video_path}")
        return 1

    logger.info(f"Testing dual face functionality with video: {video_path}")

    # Test 1: Original frame analysis
    logger.info("\n" + "="*60)
    logger.info("TEST 1: Frame-by-frame analysis")
    logger.info("="*60)
    analyze_video_frames(video_path, analysis_output_dir)

    # Test 2: New segment-based processing
    logger.info("\n" + "="*60)
    logger.info("TEST 2: Segment-based dual-face processing")
    logger.info("="*60)
    test_segment_based_processing(video_path, processing_output_dir)

    logger.info("\n" + "="*60)
    logger.info("All tests completed!")
    logger.info(f"Frame analysis results: {analysis_output_dir}")
    logger.info(f"Processing results: {processing_output_dir}")
    logger.info("="*60)

    return 0

if __name__ == "__main__":
    sys.exit(main())
