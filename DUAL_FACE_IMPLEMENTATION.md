# Dual Face Detection Implementation

## Overview

This implementation adds comprehensive dual-face detection and processing capabilities to handle videos with mixed face count scenarios. The system can intelligently detect when videos contain segments with different numbers of faces and apply appropriate processing strategies.

## Key Features

### 1. Face Count Segment Analysis
- **Function**: `FaceDetectionEngine.analyze_face_count_segments()`
- **Purpose**: Analyzes entire video timeline to detect changes in face count
- **Process**:
  - Samples video at regular intervals (default: 2 seconds)
  - Detects faces in each sample frame
  - Groups consecutive samples with same face count into segments
  - Returns list of segments with timing and face count information

### 2. Dual Face Processing
- **Class**: `DualFaceProcessor`
- **Purpose**: Implements the crop-and-stack approach for dual-face segments
- **Process**:
  1. Sort faces by x-position (left to right)
  2. Crop left-most face with padding
  3. Crop right-most face with padding
  4. Resize both faces to target width (720px)
  5. Stack vertically using ffmpeg vstack filter

### 3. Segment-Based Processing
- **Class**: `SegmentBasedProcessor`
- **Purpose**: Orchestrates processing of mixed scenarios
- **Strategies**:
  - **0 faces**: Center crop
  - **1 face**: Face centering (center face in frame)
  - **2 faces**: Dual-face stacking
  - **3+ faces**: Group centering

## Implementation Details

### Face Count Segment Detection

```python
def analyze_face_count_segments(self, video_path: str, sample_interval: float = 2.0):
    # Sample video at regular intervals
    # Detect faces in each sample
    # Group consecutive samples with same face count
    # Return segment information
```

### Dual Face Processing Commands

The implementation uses the exact ffmpeg commands as specified:

1. **Crop Face 1 (Left)**:
   ```bash
   ffmpeg -i input.mp4 -filter:v "crop=w:h:x1:y1" face1.mp4
   ```

2. **Crop Face 2 (Right)**:
   ```bash
   ffmpeg -i input.mp4 -filter:v "crop=w:h:x2:y2" face2.mp4
   ```

3. **Resize Both to Same Width**:
   ```bash
   ffmpeg -i face1.mp4 -vf "scale=720:-1" face1_resized.mp4
   ffmpeg -i face2.mp4 -vf "scale=720:-1" face2_resized.mp4
   ```

4. **Stack Vertically**:
   ```bash
   ffmpeg -i face1_resized.mp4 -i face2_resized.mp4 -filter_complex "[0:v][1:v]vstack=inputs=2" vertical_output.mp4
   ```

### Mixed Scenario Handling

For videos with both single-face and dual-face segments:

1. **Analysis Phase**: Detect all segments and their face counts
2. **Processing Phase**: Process each segment with appropriate strategy
3. **Concatenation Phase**: Combine all segments into final video

## Example Scenario

Consider a video with the following segments:

| Time Range | Face Count | Processing Strategy |
|------------|------------|-------------------|
| 0-5s       | 1          | Single face centering |
| 5-12s      | 2          | Dual face stacking |
| 12-18s     | 1          | Single face centering |
| 18-25s     | 2          | Dual face stacking |
| 25-30s     | 0          | Center crop |

The system will:
1. Detect these 5 segments automatically
2. Process each segment with the appropriate strategy
3. Concatenate all segments into a seamless final video

## Files Modified/Added

### New Files
- `reframing/video/dual_face_processor.py` - Dual face processing logic
- `reframing/video/segment_based_processor.py` - Mixed scenario orchestration
- `test_dual_face.py` - Enhanced test script with segment processing
- `demo_dual_face_logic.py` - Logic demonstration

### Modified Files
- `reframing/face_detection/engine.py` - Added segment analysis method

## Testing

The implementation includes comprehensive testing:

1. **Frame Analysis**: Analyzes individual frames to understand face detection
2. **Segment Analysis**: Tests the new segment detection functionality
3. **Dual Face Processing**: Tests individual dual-face segment processing
4. **Mixed Scenario Processing**: Tests complete mixed scenario handling

## Usage

### Basic Segment Analysis
```python
face_engine = FaceDetectionEngine(backend="mediapipe")
segments = face_engine.analyze_face_count_segments("video.mp4")
```

### Mixed Scenario Processing
```python
processor = SegmentBasedProcessor(face_engine)
success = processor.process_mixed_face_video(
    "input.mp4", "output.mp4", 
    target_width=720, target_height=1280
)
```

### Dual Face Only Processing
```python
dual_processor = DualFaceProcessor()
success = dual_processor.process_dual_face_segment(
    "input.mp4", "output.mp4", faces, 
    start_time, end_time, target_width=720
)
```

## Benefits

1. **Intelligent Adaptation**: Automatically adapts to different face count scenarios
2. **Optimal Space Usage**: Dual faces are stacked vertically for better vertical video format
3. **Smooth Transitions**: Seamless handling of face count changes within same video
4. **Flexible Processing**: Can handle pure single-face, pure dual-face, or mixed scenarios
5. **Professional Quality**: Uses proper ffmpeg commands for high-quality output

## Future Enhancements

1. **Advanced Face Tracking**: Track individual faces across segments for consistency
2. **Smart Transitions**: Add transition effects between segments
3. **Audio Synchronization**: Ensure perfect audio sync across segment boundaries
4. **Performance Optimization**: Parallel processing of segments
5. **Quality Control**: Automatic quality assessment and fallback strategies
