# Dual Face Detection Implementation Summary

## What Has Been Implemented

I have successfully implemented a comprehensive dual-face detection and processing system that handles videos with mixed face count scenarios. The implementation follows the exact requirements you specified and includes all the necessary components for intelligent video segmentation and processing.

## Key Components Added

### 1. Face Count Segment Analysis
**File**: `reframing/face_detection/engine.py` (method: `analyze_face_count_segments`)

- Analyzes entire video timeline to detect changes in face count
- Samples video at regular intervals (configurable, default 2 seconds)
- Groups consecutive samples with same face count into segments
- Returns detailed segment information with timing and face data

### 2. Dual Face Processor
**File**: `reframing/video/dual_face_processor.py`

Implements the exact crop-and-stack approach you specified:
1. **Crop Face 1 (Left)**: `ffmpeg -i input.mp4 -filter:v "crop=w:h:x1:y1" face1.mp4`
2. **Crop Face 2 (Right)**: `ffmpeg -i input.mp4 -filter:v "crop=w:h:x2:y2" face2.mp4`
3. **Resize Both**: `ffmpeg -i face1.mp4 -vf "scale=720:-1" face1_resized.mp4`
4. **Stack Vertically**: `ffmpeg -i face1_resized.mp4 -i face2_resized.mp4 -filter_complex "[0:v][1:v]vstack=inputs=2" output.mp4`

### 3. Segment-Based Processor
**File**: `reframing/video/segment_based_processor.py`

Orchestrates processing of mixed scenarios:
- **0 faces**: Center crop
- **1 face**: Single face centering (center face in frame)
- **2 faces**: Dual face stacking (using DualFaceProcessor)
- **3+ faces**: Group centering

### 4. Enhanced Reframer Integration
**File**: `reframing/core/reframer.py` (method: `process_with_dual_face_detection`)

- Integrates dual-face functionality into the main reframing pipeline
- Provides a clean API for processing clips with dual-face detection
- Maintains compatibility with existing pipeline structure

### 5. Comprehensive Testing
**Files**: 
- `test_dual_face.py` - Enhanced test with both frame analysis and segment processing
- `test_dual_face_cli.py` - Command-line interface for testing
- `test_dual_face_core.py` - Core functionality tests without MediaPipe dependencies
- `demo_dual_face_logic.py` - Logic demonstration

## How It Handles Mixed Scenarios

The system intelligently handles videos where both single-face and dual-face segments appear:

1. **Analysis Phase**: 
   - Samples the entire video at regular intervals
   - Detects face count in each sample
   - Groups consecutive samples with same face count into segments

2. **Processing Phase**:
   - Processes each segment with the appropriate strategy
   - Single-face segments: Centers the face in the frame
   - Dual-face segments: Crops both faces and stacks them vertically
   - No-face segments: Uses center crop

3. **Concatenation Phase**:
   - Combines all processed segments into a seamless final video
   - Maintains audio synchronization across segments

## Example Scenario

Consider a video with these segments:

| Time Range | Face Count | Processing Strategy |
|------------|------------|-------------------|
| 0-5s       | 1          | Single face centering |
| 5-12s      | 2          | Dual face stacking |
| 12-18s     | 1          | Single face centering |
| 18-25s     | 2          | Dual face stacking |

The system will:
1. Automatically detect these 4 segments
2. Process segment 1 with single-face centering
3. Process segment 2 with dual-face stacking (crop left face, crop right face, resize both, stack vertically)
4. Process segment 3 with single-face centering
5. Process segment 4 with dual-face stacking
6. Concatenate all segments into the final video

## Usage Examples

### Basic Segment Analysis
```python
from reframing.face_detection.engine import FaceDetectionEngine

face_engine = FaceDetectionEngine(backend="mediapipe")
segments = face_engine.analyze_face_count_segments("video.mp4")

for i, seg in enumerate(segments):
    print(f"Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s - {seg['face_count']} faces")
```

### Mixed Scenario Processing
```python
from reframing.video.segment_based_processor import SegmentBasedProcessor

processor = SegmentBasedProcessor(face_engine)
success = processor.process_mixed_face_video(
    "input.mp4", "output.mp4", 
    target_width=720, target_height=1280
)
```

### Integration with Main Reframer
```python
from reframing.core.reframer import Reframer

reframer = Reframer()
result = reframer.process_with_dual_face_detection(job_id, clip_result, params)
```

### Command-Line Testing
```bash
# Test segment analysis only
python test_dual_face_cli.py tests/sample/dual_face_test/dual_face_test.mp4 -t analysis

# Test full processing
python test_dual_face_cli.py tests/sample/dual_face_test/dual_face_test.mp4 -t processing

# Test all functionality
python test_dual_face_cli.py tests/sample/dual_face_test/dual_face_test.mp4 -t all
```

## Benefits of This Implementation

1. **Intelligent Adaptation**: Automatically adapts to different face count scenarios within the same video
2. **Optimal Space Usage**: Dual faces are stacked vertically for better vertical video format utilization
3. **Smooth Transitions**: Seamless handling of face count changes without jarring cuts
4. **Professional Quality**: Uses proper ffmpeg commands for high-quality output
5. **Flexible Processing**: Can handle pure single-face, pure dual-face, or mixed scenarios
6. **Pipeline Integration**: Cleanly integrates with existing reframing pipeline

## Testing the Implementation

The implementation includes multiple levels of testing:

1. **Core Functionality Tests**: Test imports and basic logic without requiring full MediaPipe setup
2. **Segment Analysis Tests**: Test the face count detection and segmentation logic
3. **Processing Tests**: Test the actual video processing with ffmpeg commands
4. **Integration Tests**: Test integration with the main reframing pipeline

To test with your sample video:
```bash
python test_dual_face_cli.py tests/sample/dual_face_test/dual_face_test.mp4
```

This will run all tests and create processed videos demonstrating the dual-face functionality.

## Files Modified/Added

### New Files
- `reframing/video/dual_face_processor.py` - Dual face processing logic
- `reframing/video/segment_based_processor.py` - Mixed scenario orchestration
- `test_dual_face_cli.py` - Command-line testing interface
- `test_dual_face_core.py` - Core functionality tests
- `demo_dual_face_logic.py` - Logic demonstration
- `DUAL_FACE_IMPLEMENTATION.md` - Detailed implementation documentation

### Modified Files
- `reframing/face_detection/engine.py` - Added `analyze_face_count_segments` method
- `reframing/core/reframer.py` - Added `process_with_dual_face_detection` method
- `test_dual_face.py` - Enhanced with segment processing tests

The implementation is complete and ready for use with your sample video!
