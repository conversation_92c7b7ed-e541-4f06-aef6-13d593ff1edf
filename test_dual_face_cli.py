#!/usr/bin/env python3
"""
Command-line interface for testing dual-face functionality
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging(level=logging.INFO):
    """Setup logging configuration"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_segment_analysis(video_path: str, output_dir: str):
    """Test face count segment analysis"""
    logger = logging.getLogger(__name__)
    
    try:
        from reframing.face_detection.engine import FaceDetectionEngine
        
        logger.info("Initializing face detection engine...")
        face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)
        
        logger.info(f"Analyzing face count segments in: {video_path}")
        segments = face_engine.analyze_face_count_segments(video_path, sample_interval=1.0)
        
        if segments:
            logger.info(f"Detected {len(segments)} segments:")
            
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Save segment analysis
            analysis_file = os.path.join(output_dir, "segment_analysis.txt")
            with open(analysis_file, 'w') as f:
                f.write("Face Count Segment Analysis\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Video: {video_path}\n\n")
                
                for i, seg in enumerate(segments):
                    duration = seg['end_time'] - seg['start_time']
                    logger.info(f"  Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s ({duration:.1f}s) - {seg['face_count']} faces")
                    
                    f.write(f"Segment {i+1}:\n")
                    f.write(f"  Time: {seg['start_time']:.1f}s - {seg['end_time']:.1f}s ({duration:.1f}s)\n")
                    f.write(f"  Face count: {seg['face_count']}\n")
                    f.write(f"  Samples: {len(seg['samples'])}\n")
                    f.write("\n")
            
            logger.info(f"Analysis saved to: {analysis_file}")
            return True
        else:
            logger.warning("No segments detected")
            return False
            
    except Exception as e:
        logger.error(f"Segment analysis failed: {str(e)}")
        return False

def test_dual_face_processing(video_path: str, output_dir: str):
    """Test dual-face video processing"""
    logger = logging.getLogger(__name__)
    
    try:
        from reframing.face_detection.engine import FaceDetectionEngine
        from reframing.video.segment_based_processor import SegmentBasedProcessor
        
        logger.info("Initializing processors...")
        face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)
        processor = SegmentBasedProcessor(face_engine)
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        output_video = os.path.join(output_dir, "dual_face_processed.mp4")
        
        logger.info(f"Processing video with dual-face detection...")
        logger.info(f"Input: {video_path}")
        logger.info(f"Output: {output_video}")
        
        success = processor.process_mixed_face_video(
            video_path, output_video, target_width=720, target_height=1280
        )
        
        if success:
            logger.info(f"✓ Successfully created processed video: {output_video}")
            return True
        else:
            logger.error("✗ Failed to process video")
            return False
            
    except Exception as e:
        logger.error(f"Dual-face processing failed: {str(e)}")
        return False

def test_reframer_integration(video_path: str, output_dir: str):
    """Test integration with the main reframer"""
    logger = logging.getLogger(__name__)
    
    try:
        from reframing.core.reframer import Reframer
        from reframing.config.settings import ReframingConfig
        
        logger.info("Initializing reframer...")
        config = ReframingConfig.from_env()
        reframer = Reframer(config=config, pipeline_output_dir=output_dir)
        
        # Create mock clip result
        clip_result = {
            'clips': [
                {
                    'id': 'test_clip',
                    'path': video_path,
                    'start_time': 0.0,
                    'end_time': 30.0
                }
            ]
        }
        
        params = {
            'output_format': 'vertical',
            'target_width': 720,
            'target_height': 1280
        }
        
        logger.info("Testing dual-face processing integration...")
        result = reframer.process_with_dual_face_detection("test_job", clip_result, params)
        
        if result.get('dual_face_processing_completed'):
            processed_clips = result.get('dual_face_clips', [])
            logger.info(f"✓ Successfully processed {len(processed_clips)} clips")
            
            for clip in processed_clips:
                dual_face_path = clip.get('dual_face_path')
                if dual_face_path and os.path.exists(dual_face_path):
                    logger.info(f"  Created: {dual_face_path}")
            
            return True
        else:
            logger.error("✗ Dual-face processing not completed")
            return False
            
    except Exception as e:
        logger.error(f"Reframer integration test failed: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Test dual-face functionality")
    parser.add_argument("video", help="Path to test video file")
    parser.add_argument("-o", "--output", default="output/dual_face_test", 
                       help="Output directory (default: output/dual_face_test)")
    parser.add_argument("-t", "--test", choices=["analysis", "processing", "integration", "all"],
                       default="all", help="Test to run (default: all)")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    
    # Check if video file exists
    if not os.path.exists(args.video):
        logger.error(f"Video file not found: {args.video}")
        return 1
    
    logger.info(f"Testing dual-face functionality with: {args.video}")
    logger.info(f"Output directory: {args.output}")
    
    success_count = 0
    total_tests = 0
    
    # Run tests based on selection
    if args.test in ["analysis", "all"]:
        logger.info("\n" + "="*60)
        logger.info("TEST: Face Count Segment Analysis")
        logger.info("="*60)
        total_tests += 1
        if test_segment_analysis(args.video, args.output):
            success_count += 1
    
    if args.test in ["processing", "all"]:
        logger.info("\n" + "="*60)
        logger.info("TEST: Dual-Face Video Processing")
        logger.info("="*60)
        total_tests += 1
        if test_dual_face_processing(args.video, args.output):
            success_count += 1
    
    if args.test in ["integration", "all"]:
        logger.info("\n" + "="*60)
        logger.info("TEST: Reframer Integration")
        logger.info("="*60)
        total_tests += 1
        if test_reframer_integration(args.video, args.output):
            success_count += 1
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Tests passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 All tests PASSED!")
        return 0
    else:
        logger.warning(f"⚠️  {total_tests - success_count} test(s) failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
