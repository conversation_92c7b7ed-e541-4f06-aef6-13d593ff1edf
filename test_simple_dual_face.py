#!/usr/bin/env python3
"""
Simple test for dual face detection functionality
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_face_engine_import():
    """Test if we can import and initialize the face engine"""
    try:
        from reframing.face_detection.engine import FaceDetectionEngine
        
        logger = logging.getLogger(__name__)
        logger.info("Successfully imported FaceDetectionEngine")
        
        # Try to initialize with MediaPipe
        face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)
        logger.info("Successfully initialized FaceDetectionEngine with MediaPipe")
        
        return face_engine
    except Exception as e:
        logger.error(f"Failed to import/initialize FaceDetectionEngine: {str(e)}")
        return None

def test_segment_analysis():
    """Test the new segment analysis functionality"""
    logger = logging.getLogger(__name__)
    
    face_engine = test_face_engine_import()
    if not face_engine:
        return False
    
    video_path = "tests/sample/dual_face_test/dual_face_test.mp4"
    
    if not os.path.exists(video_path):
        logger.error(f"Video file not found: {video_path}")
        return False
    
    logger.info(f"Testing segment analysis on: {video_path}")
    
    try:
        segments = face_engine.analyze_face_count_segments(video_path, sample_interval=2.0)
        
        if segments:
            logger.info(f"Successfully detected {len(segments)} segments:")
            for i, seg in enumerate(segments):
                duration = seg['end_time'] - seg['start_time']
                logger.info(f"  Segment {i+1}: {seg['start_time']:.1f}s-{seg['end_time']:.1f}s ({duration:.1f}s) - {seg['face_count']} faces")
            return True
        else:
            logger.warning("No segments detected")
            return False
            
    except Exception as e:
        logger.error(f"Segment analysis failed: {str(e)}")
        return False

def main():
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting simple dual face test...")
    
    # Test 1: Basic import and initialization
    logger.info("\n=== Test 1: Import and Initialization ===")
    if not test_face_engine_import():
        logger.error("Failed basic import test")
        return 1
    
    # Test 2: Segment analysis
    logger.info("\n=== Test 2: Segment Analysis ===")
    if test_segment_analysis():
        logger.info("Segment analysis test passed!")
    else:
        logger.error("Segment analysis test failed")
        return 1
    
    logger.info("\nAll tests passed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
